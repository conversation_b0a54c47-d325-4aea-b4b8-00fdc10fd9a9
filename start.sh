#!/bin/bash

# QAFlow MVP Startup Script
# This script helps you get QAF<PERSON> up and running quickly

set -e

echo "🚀 QAFlow MVP Startup Script"
echo "=============================="

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check if .env file exists
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "✅ .env file created. Please edit it with your configuration."
    echo "⚠️  Don't forget to add your AI provider API keys!"
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p n8n/workflows n8n/credentials postgres/init caddy

# Set proper permissions
echo "🔐 Setting proper permissions..."
chmod +x docker-entrypoint.sh branded-ui/docker-entrypoint.sh 2>/dev/null || true

# Pull latest images
echo "📦 Pulling latest Docker images..."
docker-compose pull

# Build the branded UI
echo "🏗️  Building branded UI..."
docker-compose build branded-ui

# Start the services
echo "🚀 Starting QAFlow services..."
docker-compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 10

# Check service status
echo "🔍 Checking service status..."
docker-compose ps

echo ""
echo "✅ QAFlow MVP is starting up!"
echo ""
echo "📊 Access points:"
echo "   • Branded UI: http://localhost:3000"
echo "   • n8n Editor: http://localhost:5678"
echo "   • Reverse Proxy: http://localhost:80"
echo ""
echo "🔑 Default credentials:"
echo "   • Username: admin"
echo "   • Password: qaflow_admin_2024"
echo ""
echo "📝 Next steps:"
echo "   1. Open http://localhost:3000 in your browser"
echo "   2. Log in with the credentials above"
echo "   3. Configure your AI provider API keys in Settings"
echo "   4. Create your first QA workflow"
echo ""
echo "📚 Documentation: See README.md for more details"
echo "🐛 Logs: Run 'docker-compose logs -f' to view logs"
echo "🛑 Stop: Run 'docker-compose down' to stop all services"
