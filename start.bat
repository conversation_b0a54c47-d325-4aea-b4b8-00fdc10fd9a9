@echo off
REM QAFlow MVP Startup Script for Windows
REM This script helps you get Q<PERSON><PERSON> up and running quickly

echo [*] QAFlow MVP Startup Script
echo ==============================

REM Check if Docker is installed
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker is not installed. Please install Docker Desktop first.
    pause
    exit /b 1
)

REM Check if Docker Compose is installed
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker Compose is not installed. Please install Docker Compose first.
    pause
    exit /b 1
)

REM Check if .env file exists
if not exist .env (
    echo [INFO] Creating .env file from template...
    copy .env.example .env
    echo [SUCCESS] .env file created. Please edit it with your configuration.
    echo [WARNING] Don't forget to add your AI provider API keys!
)

REM Create necessary directories
echo [INFO] Creating necessary directories...
if not exist n8n\workflows mkdir n8n\workflows
if not exist n8n\credentials mkdir n8n\credentials
if not exist postgres\init mkdir postgres\init
if not exist caddy mkdir caddy

REM Pull latest images
echo [INFO] Pulling latest Docker images...
docker-compose pull

REM Build the branded UI
echo [INFO] Building branded UI...
docker-compose build branded-ui

REM Start the services
echo [INFO] Starting QAFlow services...
docker-compose up -d

REM Wait for services to be ready
echo [INFO] Waiting for services to start...
timeout /t 10 /nobreak >nul

REM Check service status
echo [INFO] Checking service status...
docker-compose ps

echo.
echo [SUCCESS] QAFlow MVP is starting up!
echo.
echo [ACCESS] Access points:
echo    * Branded UI: http://localhost:3000
echo    * n8n Editor: http://localhost:5678
echo    * Reverse Proxy: http://localhost:80
echo.
echo [CREDENTIALS] Default credentials:
echo    * Username: admin
echo    * Password: qaflow_admin_2024
echo.
echo [NEXT STEPS] Next steps:
echo    1. Open http://localhost:3000 in your browser
echo    2. Log in with the credentials above
echo    3. Configure your AI provider API keys in Settings
echo    4. Create your first QA workflow
echo.
echo [DOCS] Documentation: See README.md for more details
echo [LOGS] Logs: Run 'docker-compose logs -f' to view logs
echo [STOP] Stop: Run 'docker-compose down' to stop all services
echo.
pause
