services:
  # PostgreSQL database
  postgres:
    image: postgres:15-alpine
    container_name: qaflow-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: activepieces
      POSTGRES_USER: activepieces
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - qaflow-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U activepieces -d activepieces"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for queue management
  redis:
    image: redis:7-alpine
    container_name: qaflow-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    networks:
      - qaflow-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Activepieces workflow automation engine
  activepieces:
    image: activepieces/activepieces:latest
    container_name: qaflow-activepieces
    restart: unless-stopped
    environment:
      # Database configuration
      AP_POSTGRES_DATABASE: activepieces
      AP_POSTGRES_HOST: postgres
      AP_POSTGRES_PORT: 5432
      AP_POSTGRES_USERNAME: activepieces
      AP_POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      AP_POSTGRES_USE_SSL: false
      
      # Redis configuration
      AP_REDIS_HOST: redis
      AP_REDIS_PORT: 6379
      AP_REDIS_PASSWORD: ${REDIS_PASSWORD}
      AP_REDIS_DB: 0
      
      # Activepieces configuration
      AP_ENVIRONMENT: prod
      AP_FRONTEND_URL: ${AP_FRONTEND_URL:-http://localhost:8080}
      AP_WEBHOOK_TIMEOUT_SECONDS: 30
      AP_TRIGGER_DEFAULT_POLL_INTERVAL: 5
      AP_ENCRYPTION_KEY: ${AP_ENCRYPTION_KEY}
      
      # Authentication
      AP_JWT_SECRET: ${AP_JWT_SECRET}
      AP_SIGN_UP_ENABLED: true
      
      # File storage
      AP_FILE_STORAGE_LOCATION: db
      
      # Execution mode
      AP_EXECUTION_MODE: UNSANDBOXED
      
      # Logging
      AP_LOG_LEVEL: INFO
      AP_LOG_PRETTY: false
      
      # Telemetry (disable for white-label)
      AP_TELEMETRY_ENABLED: false
      
      # Timezone
      TZ: ${TIMEZONE:-UTC}
      
    ports:
      - "${AP_PORT:-8080}:80"
    volumes:
      - activepieces_data:/opt/activepieces/dist/packages/server/api
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - qaflow-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/api/v1/flags"]
      interval: 30s
      timeout: 10s
      retries: 3

  # QAFlow Branded UI (React frontend)
  qaflow-ui:
    build:
      context: ./qaflow-ui
      dockerfile: Dockerfile
    container_name: qaflow-ui
    restart: unless-stopped
    environment:
      REACT_APP_ACTIVEPIECES_API_URL: http://activepieces:80
      REACT_APP_API_BASE_URL: ${API_BASE_URL:-http://localhost:3000}
      REACT_APP_BRAND_NAME: ${BRAND_NAME:-QAFlow}
      NODE_ENV: ${NODE_ENV:-production}
    ports:
      - "${UI_PORT:-3000}:3000"
    depends_on:
      - activepieces
    networks:
      - qaflow-network
    volumes:
      - ./qaflow-ui/src:/app/src

  # Reverse proxy (Caddy for automatic HTTPS)
  caddy:
    image: caddy:2-alpine
    container_name: qaflow-proxy
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./caddy/Caddyfile:/etc/caddy/Caddyfile
      - caddy_data:/data
      - caddy_config:/config
    networks:
      - qaflow-network
    depends_on:
      - activepieces
      - qaflow-ui

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  activepieces_data:
    driver: local
  caddy_data:
    driver: local
  caddy_config:
    driver: local

networks:
  qaflow-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
