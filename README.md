# 🚀 QAFlow-MVP

A **self-hosted, portable QA automation tool** powered by **Activepieces** with **AI-provider flexibility**. Deploy it on your own server or offer it as a self-hosted SaaS to clients.

---

## 📦 Architecture Overview

- **Activepieces (Community Edition)** – Open-source workflow automation platform
- **Database** – PostgreSQL for data persistence
- **Redis** – Queue management and caching
- **AI Models** – Support for **Groq**, **OpenAI**, **Anthropic**, or any provider via flexible AI pieces
- **Trigger Sources** – Webhooks, CI tools, issue trackers, etc.
- **Execution Layer** – Shell scripts, <PERSON>, Playwright/Selenium, HTTP API
- **Notification & Approval** – Microsoft Teams, Slack, SMTP/SES
- **Reverse Proxy** – NGINX or Caddy with TLS
- **Branded UI** – React frontend interfacing with Activepieces API

---

## 🐳 Deployment

Use `docker-compose-activepieces-simple.yml` to spin up the stack:

```yaml
services:
  activepieces:
    image: activepieces/activepieces:latest
    environment:
      AP_POSTGRES_DATABASE: activepieces
      AP_POSTGRES_HOST: postgres
      AP_POSTGRES_USERNAME: activepieces
      AP_POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      AP_REDIS_HOST: redis
      AP_REDIS_PASSWORD: ${REDIS_PASSWORD}
      AP_JWT_SECRET: ${AP_JWT_SECRET}
      AP_ENCRYPTION_KEY: ${AP_ENCRYPTION_KEY}
    depends_on:
      - postgres
      - redis
    ports:
      - "8080:80"

  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: activepieces
      POSTGRES_USER: activepieces
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD}
```

### Quick Start

1. Install Docker & Docker‑Compose on your system
2. Clone this repository
3. Copy `.env.example` to `.env` and configure:

   ```env
   POSTGRES_PASSWORD=qaflow_postgres_2024
   REDIS_PASSWORD=qaflow_redis_2024
   AP_JWT_SECRET=qaflow_jwt_secret_very_long_and_secure_key_here
   AP_ENCRYPTION_KEY=qaflow_encryption_key_32_chars_long
   ```
4. Run: `docker-compose -f docker-compose-activepieces-simple.yml up -d`
5. Access `http://localhost:8080` to access Activepieces
6. Access `http://localhost:3000` for the branded QAFlow UI (when implemented)

* This follows n8n’s official Docker setup guidelines ([github.com][1], [dev.to][2], [community.n8n.io][3], [docs.n8n.io][4])

---

## 🧠 AI-Abstraction: Provider-Agnostic Flow

Use Activepieces' **flexible AI pieces** to route AI calls based on client-configured provider:

```json
[
  { "provider": "groq",     "piece": "@activepieces/piece-groq" },
  { "provider": "openai",   "piece": "@activepieces/piece-openai" },
  { "provider": "anthropic", "piece": "@activepieces/piece-anthropic" }
]
```

Clients simply supply `provider` and API key in Activepieces connections—no flow edits required.

---

## ⚙️ MVP Workflows

1. **QA Pipeline**

   * Triggered via Webhook or PR event
   * Runs tests via Jenkins or shell scripts
   * Collects logs and results
   * Sends failures to AI abstraction for failure analysis
   * Notifies human reviewers via Teams/Slack
   * Branches flow based on approval
   * Updates tickets/status in Jira or Git system
   * Logs run metadata to Postgres

2. **Admin Dashboard**

   * Lists flows and execution history
   * Lets admins configure AI provider connections and endpoints

---

## 🔐 Security & Multi-Tenancy

* Uses **Docker volumes** for data persistence
* Protected via **Basic Auth + TLS**; future support planned for OAuth/SAML
* Minimal DB schema supports **client scoping** (e.g., `client_id` prefix tags on flows)

---

## 🧩 Project Structure

```
/QAFlow-MVP
│  docker-compose-activepieces-simple.yml
│  .env
└─ branded-ui/
   ├─ src/
   ├─ package.json
   └─ Dockerfile
└─ activepieces/
   └─ flows/
       └─ mvp_qa_pipeline.json
```

* `branded-ui/`: Custom portal interfacing with Activepieces API + flows
* `activepieces/flows/`: Starter JSON export of MVP QA flow

---

## 🔧 Development Tips

* Define `docker-compose-activepieces-simple.yml`, `.env`, and base UI scaffold in VS Code
* Stub AI abstraction in `activepieces/flows/mvp_qa_pipeline.json`
* Add development comments for:

  ```js
  // TODO: implement Shell piece to run tests
  // TODO: parse logs and feed to AI piece
  ```
* Use templates to auto-generate provider configuration code in React

---

## 📈 Roadmap

| Phase | Feature                           |
| ----- | --------------------------------- |
| 0     | Setup Docker, branded UI stub     |
| 1     | QA workflow + AI + Teams          |
| 2     | Provider selection UI             |
| 3     | Metrics dashboard + role controls |

---

## ✅ Next Steps

* Clone this repo to your server
* Run components with `docker-compose up -d`
* Kick off IDE auto-fill using Copilot
* Test end-to-end flow with a basic PR webhook


[1]: https://github.com/n8n-io/n8n-hosting?utm_source=chatgpt.com "n8n-hosting - GitHub"
[2]: https://dev.to/ralphsebastian/how-to-selfhost-n8n-in-cloudlocally-with-docker-4n04?utm_source=chatgpt.com "How to Selfhost n8n in Cloud/Locally with Docker - DEV Community"
[3]: https://community.n8n.io/t/make-a-local-n8n-container-accessible-via-ssl-https/84959?utm_source=chatgpt.com "Make a local n8n container accessible via SSL/HTTPS - Questions"
[4]: https://docs.n8n.io/hosting/installation/server-setups/docker-compose/?utm_source=chatgpt.com "Docker Compose - n8n Docs"
