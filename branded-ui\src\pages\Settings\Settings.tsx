import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  TextField,
  Button,
  Grid,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Divider,
} from '@mui/material';
import { Save, Science } from '@mui/icons-material';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index, ...other }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`settings-tabpanel-${index}`}
      aria-labelledby={`settings-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

const Settings: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [aiProvider, setAiProvider] = useState('groq');
  const [enableNotifications, setEnableNotifications] = useState(true);
  const [enableMetrics, setEnableMetrics] = useState(true);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleSave = () => {
    // TODO: Implement settings save functionality
    alert('Settings save functionality will be implemented in Phase 2');
  };

  const handleTestConnection = () => {
    // TODO: Implement connection testing
    alert('Connection testing will be implemented in Phase 2');
  };

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        Settings
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        Configure your QAFlow platform settings
      </Typography>

      <Paper sx={{ width: '100%' }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange} aria-label="settings tabs">
            <Tab label="AI Providers" />
            <Tab label="Notifications" />
            <Tab label="General" />
            <Tab label="Security" />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <Typography variant="h6" gutterBottom>
            AI Provider Configuration
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Configure AI providers for automated analysis and insights
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Default AI Provider</InputLabel>
                <Select
                  value={aiProvider}
                  label="Default AI Provider"
                  onChange={(e) => setAiProvider(e.target.value)}
                >
                  <MenuItem value="groq">Groq</MenuItem>
                  <MenuItem value="openai">OpenAI</MenuItem>
                  <MenuItem value="anthropic">Anthropic</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>

          <Box mt={3}>
            <Typography variant="h6" gutterBottom>
              Provider Credentials
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom>
                      Groq Configuration
                    </Typography>
                    <TextField
                      fullWidth
                      label="API Key"
                      type="password"
                      margin="normal"
                      placeholder="Enter Groq API key"
                    />
                    <TextField
                      fullWidth
                      label="Model"
                      margin="normal"
                      defaultValue="llama3-8b-8192"
                    />
                    <Box mt={2}>
                      <Button
                        variant="outlined"
                        startIcon={<Science />}
                        onClick={handleTestConnection}
                      >
                        Test Connection
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom>
                      OpenAI Configuration
                    </Typography>
                    <TextField
                      fullWidth
                      label="API Key"
                      type="password"
                      margin="normal"
                      placeholder="Enter OpenAI API key"
                    />
                    <TextField
                      fullWidth
                      label="Model"
                      margin="normal"
                      defaultValue="gpt-4"
                    />
                    <Box mt={2}>
                      <Button
                        variant="outlined"
                        startIcon={<Science />}
                        onClick={handleTestConnection}
                      >
                        Test Connection
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Typography variant="h6" gutterBottom>
            Notification Settings
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Configure how and when you receive notifications
          </Typography>

          <FormControlLabel
            control={
              <Switch
                checked={enableNotifications}
                onChange={(e) => setEnableNotifications(e.target.checked)}
              />
            }
            label="Enable notifications"
          />

          <Divider sx={{ my: 3 }} />

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle1" gutterBottom>
                Email Configuration
              </Typography>
              <TextField
                fullWidth
                label="SMTP Host"
                margin="normal"
                placeholder="smtp.gmail.com"
              />
              <TextField
                fullWidth
                label="SMTP Port"
                margin="normal"
                placeholder="587"
              />
              <TextField
                fullWidth
                label="Username"
                margin="normal"
                placeholder="<EMAIL>"
              />
              <TextField
                fullWidth
                label="Password"
                type="password"
                margin="normal"
                placeholder="App password"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography variant="subtitle1" gutterBottom>
                Slack Configuration
              </Typography>
              <TextField
                fullWidth
                label="Bot Token"
                type="password"
                margin="normal"
                placeholder="xoxb-your-slack-bot-token"
              />
              <TextField
                fullWidth
                label="Default Channel"
                margin="normal"
                placeholder="#qa-alerts"
              />
              
              <Typography variant="subtitle1" gutterBottom sx={{ mt: 3 }}>
                Microsoft Teams
              </Typography>
              <TextField
                fullWidth
                label="Webhook URL"
                margin="normal"
                placeholder="https://your-org.webhook.office.com/..."
              />
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Typography variant="h6" gutterBottom>
            General Settings
          </Typography>
          
          <FormControlLabel
            control={
              <Switch
                checked={enableMetrics}
                onChange={(e) => setEnableMetrics(e.target.checked)}
              />
            }
            label="Enable metrics collection"
          />

          <Box mt={3}>
            <TextField
              fullWidth
              label="Test Timeout (seconds)"
              type="number"
              margin="normal"
              defaultValue="3600"
            />
            <TextField
              fullWidth
              label="Log Retention (days)"
              type="number"
              margin="normal"
              defaultValue="30"
            />
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={3}>
          <Typography variant="h6" gutterBottom>
            Security Settings
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Configure security and access control settings
          </Typography>

          <TextField
            fullWidth
            label="Session Timeout (minutes)"
            type="number"
            margin="normal"
            defaultValue="60"
          />

          <FormControlLabel
            control={<Switch defaultChecked />}
            label="Require HTTPS"
          />

          <FormControlLabel
            control={<Switch defaultChecked />}
            label="Enable rate limiting"
          />
        </TabPanel>

        <Box sx={{ p: 3, borderTop: 1, borderColor: 'divider' }}>
          <Button
            variant="contained"
            startIcon={<Save />}
            onClick={handleSave}
          >
            Save Settings
          </Button>
        </Box>
      </Paper>
    </Box>
  );
};

export default Settings;
