services:
  # n8n workflow automation engine
  n8n:
    image: n8nio/n8n:latest
    container_name: qaflow-n8n
    restart: unless-stopped
    environment:
      # Database configuration
      DB_TYPE: postgresdb
      DB_POSTGRESDB_HOST: postgres
      DB_POSTGRESDB_PORT: 5432
      DB_POSTGRESDB_DATABASE: n8n
      DB_POSTGRESDB_USER: n8n
      DB_POSTGRESDB_PASSWORD: ${POSTGRES_PASSWORD}
      
      # n8n configuration
      N8N_HOST: ${N8N_HOST:-localhost}
      N8N_PORT: 5678
      N8N_PROTOCOL: ${N8N_PROTOCOL:-http}
      WEBHOOK_URL: ${WEBHOOK_URL:-http://localhost:5678}
      
      # Authentication
      N8N_BASIC_AUTH_ACTIVE: 'true'
      N8N_BASIC_AUTH_USER: ${N8N_USER}
      N8N_BASIC_AUTH_PASSWORD: ${N8N_PASSWORD}
      
      # Execution settings
      EXECUTIONS_PROCESS: main
      EXECUTIONS_MODE: regular
      QUEUE_BULL_REDIS_HOST: redis
      QUEUE_BULL_REDIS_PORT: 6379
      QUEUE_BULL_REDIS_DB: 0
      
      # Security
      N8N_SECURE_COOKIE: ${N8N_SECURE_COOKIE:-false}
      N8N_ENCRYPTION_KEY: ${N8N_ENCRYPTION_KEY}
      
      # Logging
      N8N_LOG_LEVEL: info
      N8N_LOG_OUTPUT: console
      
      # Timezone
      GENERIC_TIMEZONE: ${TIMEZONE:-UTC}
      TZ: ${TIMEZONE:-UTC}
      
    ports:
      - "${N8N_PORT:-5678}:5678"
    volumes:
      - ./n8n:/home/<USER>/.n8n
      - ./n8n/workflows:/home/<USER>/.n8n/workflows
      - ./n8n/credentials:/home/<USER>/.n8n/credentials
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - qaflow-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:5678/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL database
  postgres:
    image: postgres:15-alpine
    container_name: qaflow-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: n8n
      POSTGRES_USER: n8n
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./postgres/init:/docker-entrypoint-initdb.d
    networks:
      - qaflow-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U n8n -d n8n"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for queue management
  redis:
    image: redis:7-alpine
    container_name: qaflow-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    networks:
      - qaflow-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Branded UI (React frontend)
  branded-ui:
    build:
      context: ./branded-ui
      dockerfile: Dockerfile
    container_name: qaflow-ui
    restart: unless-stopped
    environment:
      REACT_APP_N8N_API_URL: http://n8n:5678
      REACT_APP_API_BASE_URL: ${API_BASE_URL:-http://localhost:3000}
      NODE_ENV: ${NODE_ENV:-production}
    ports:
      - "${UI_PORT:-3000}:3000"
    depends_on:
      - n8n
    networks:
      - qaflow-network
    volumes:
      - ./branded-ui/src:/app/src
      - ./branded-ui/public:/app/public

  # Reverse proxy (Caddy for automatic HTTPS)
  caddy:
    image: caddy:2-alpine
    container_name: qaflow-proxy
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./caddy/Caddyfile:/etc/caddy/Caddyfile
      - caddy_data:/data
      - caddy_config:/config
    networks:
      - qaflow-network
    depends_on:
      - n8n
      - branded-ui

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  caddy_data:
    driver: local
  caddy_config:
    driver: local

networks:
  qaflow-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
