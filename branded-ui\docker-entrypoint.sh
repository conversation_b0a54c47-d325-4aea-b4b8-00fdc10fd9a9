#!/bin/sh

# QAFlow Branded UI Docker Entrypoint Script
# This script handles environment variable substitution and starts nginx

set -e

# Function to substitute environment variables in files
substitute_env_vars() {
    local file="$1"
    if [ -f "$file" ]; then
        echo "Substituting environment variables in $file"
        envsubst < "$file" > "${file}.tmp" && mv "${file}.tmp" "$file"
    fi
}

# Create runtime configuration from environment variables
cat > /usr/share/nginx/html/config.js << EOF
window.QAFlowConfig = {
    API_BASE_URL: '${REACT_APP_API_BASE_URL:-http://localhost:3000}',
    N8N_API_URL: '${REACT_APP_N8N_API_URL:-http://localhost:5678}',
    APP_NAME: '${REACT_APP_NAME:-QAFlow}',
    APP_VERSION: '${REACT_APP_VERSION:-1.0.0}',
    ENVIRONMENT: '${NODE_ENV:-production}',
    FEATURES: {
        MULTI_TENANT: ${ENABLE_MULTI_TENANT:-false},
        METRICS: ${ENABLE_METRICS:-true},
        DEBUG: ${DEBUG_MODE:-false}
    }
};
EOF

echo "Generated runtime configuration:"
cat /usr/share/nginx/html/config.js

# Substitute environment variables in nginx config if needed
substitute_env_vars /etc/nginx/nginx.conf

# Validate nginx configuration
echo "Validating nginx configuration..."
nginx -t

echo "Starting QAFlow Branded UI..."
exec "$@"
