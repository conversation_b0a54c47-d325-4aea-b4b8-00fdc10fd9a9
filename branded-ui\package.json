{"name": "qaflow-branded-ui", "version": "1.0.0", "description": "QAFlow MVP - Branded <PERSON><PERSON> for n8n QA Orchestration Platform", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.23", "@types/react": "^18.0.37", "@types/react-dom": "^18.0.11", "axios": "^1.4.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.10.0", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "@mui/material": "^5.12.3", "@mui/icons-material": "^5.12.3", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/x-data-grid": "^6.3.1", "@mui/x-date-pickers": "^6.3.1", "recharts": "^2.6.2", "date-fns": "^2.30.0", "react-query": "^3.39.3", "react-hook-form": "^7.43.9", "@hookform/resolvers": "^3.1.0", "yup": "^1.1.1", "react-hot-toast": "^2.4.1", "lodash": "^4.17.21", "@types/lodash": "^4.14.194"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-router-dom": "^5.3.3", "eslint": "^8.40.0", "@typescript-eslint/eslint-plugin": "^5.59.2", "@typescript-eslint/parser": "^5.59.2"}, "proxy": "http://localhost:5678"}