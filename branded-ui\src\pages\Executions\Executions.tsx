import React from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Tooltip,
} from '@mui/material';
import { Visibility, Refresh } from '@mui/icons-material';

const Executions: React.FC = () => {
  // Mock data - replace with real data from n8n API
  const executions = [
    {
      id: 'exec_001',
      workflowName: 'PR Quality Check',
      status: 'success',
      startTime: '2024-01-15 14:30:00',
      endTime: '2024-01-15 14:32:34',
      duration: '2m 34s',
      trigger: 'webhook',
    },
    {
      id: 'exec_002',
      workflowName: 'Nightly Test Suite',
      status: 'failed',
      startTime: '2024-01-15 02:00:00',
      endTime: '2024-01-15 02:15:22',
      duration: '15m 22s',
      trigger: 'schedule',
    },
    {
      id: 'exec_003',
      workflowName: 'API Integration Tests',
      status: 'success',
      startTime: '2024-01-14 16:45:00',
      endTime: '2024-01-14 16:50:18',
      duration: '5m 18s',
      trigger: 'manual',
    },
    {
      id: 'exec_004',
      workflowName: 'Security Scan',
      status: 'running',
      startTime: '2024-01-14 15:20:00',
      endTime: null,
      duration: '3m 12s',
      trigger: 'webhook',
    },
    {
      id: 'exec_005',
      workflowName: 'PR Quality Check',
      status: 'success',
      startTime: '2024-01-14 10:15:00',
      endTime: '2024-01-14 10:17:45',
      duration: '2m 45s',
      trigger: 'webhook',
    },
  ];

  const getStatusChip = (status: string) => {
    switch (status) {
      case 'success':
        return <Chip label="Success" color="success" size="small" />;
      case 'failed':
        return <Chip label="Failed" color="error" size="small" />;
      case 'running':
        return <Chip label="Running" color="warning" size="small" />;
      case 'waiting':
        return <Chip label="Waiting" color="info" size="small" />;
      default:
        return <Chip label="Unknown" color="default" size="small" />;
    }
  };

  const getTriggerChip = (trigger: string) => {
    const color = trigger === 'webhook' ? 'primary' : trigger === 'schedule' ? 'secondary' : 'default';
    return <Chip label={trigger} color={color} size="small" variant="outlined" />;
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Executions
          </Typography>
          <Typography variant="body1" color="text.secondary">
            View and monitor workflow execution history
          </Typography>
        </Box>
        <Tooltip title="Refresh executions">
          <IconButton
            onClick={() => {
              // TODO: Implement refresh functionality
              alert('Refresh functionality will be implemented in Phase 1');
            }}
          >
            <Refresh />
          </IconButton>
        </Tooltip>
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Execution ID</TableCell>
              <TableCell>Workflow</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Trigger</TableCell>
              <TableCell>Start Time</TableCell>
              <TableCell>Duration</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {executions.map((execution) => (
              <TableRow key={execution.id} hover>
                <TableCell>
                  <Typography variant="body2" fontFamily="monospace">
                    {execution.id}
                  </Typography>
                </TableCell>
                <TableCell>{execution.workflowName}</TableCell>
                <TableCell>{getStatusChip(execution.status)}</TableCell>
                <TableCell>{getTriggerChip(execution.trigger)}</TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {execution.startTime}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" fontFamily="monospace">
                    {execution.duration}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Tooltip title="View execution details">
                    <IconButton
                      size="small"
                      onClick={() => {
                        // TODO: Implement execution details view
                        alert('Execution details will be implemented in Phase 1');
                      }}
                    >
                      <Visibility />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default Executions;
