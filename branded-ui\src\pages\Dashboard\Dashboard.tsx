import React from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Paper,
  Chip,
} from '@mui/material';
import {
  TrendingUp,
  CheckCircle,
  Error,
  Schedule,
  PlayArrow,
} from '@mui/icons-material';

const Dashboard: React.FC = () => {
  // Mock data - replace with real data from API
  const stats = {
    totalExecutions: 156,
    successfulExecutions: 142,
    failedExecutions: 14,
    activeWorkflows: 8,
  };

  const recentExecutions = [
    {
      id: 1,
      workflowName: 'PR Quality Check',
      status: 'success',
      duration: '2m 34s',
      timestamp: '2024-01-15 14:30:00',
    },
    {
      id: 2,
      workflowName: 'Nightly Test Suite',
      status: 'failed',
      duration: '15m 22s',
      timestamp: '2024-01-15 02:00:00',
    },
    {
      id: 3,
      workflowName: 'API Integration Tests',
      status: 'success',
      duration: '5m 18s',
      timestamp: '2024-01-14 16:45:00',
    },
    {
      id: 4,
      workflowName: 'Security Scan',
      status: 'running',
      duration: '3m 12s',
      timestamp: '2024-01-14 15:20:00',
    },
  ];

  const StatCard = ({ title, value, icon, color }: any) => (
    <Card>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>
            <Typography color="textSecondary" gutterBottom variant="body2">
              {title}
            </Typography>
            <Typography variant="h4" component="h2">
              {value}
            </Typography>
          </Box>
          <Box sx={{ color: color }}>
            {icon}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  const getStatusChip = (status: string) => {
    switch (status) {
      case 'success':
        return <Chip label="Success" color="success" size="small" />;
      case 'failed':
        return <Chip label="Failed" color="error" size="small" />;
      case 'running':
        return <Chip label="Running" color="warning" size="small" />;
      default:
        return <Chip label="Unknown" color="default" size="small" />;
    }
  };

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        Dashboard
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        Overview of your QA automation platform
      </Typography>

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Executions"
            value={stats.totalExecutions}
            icon={<TrendingUp fontSize="large" />}
            color="primary.main"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Successful"
            value={stats.successfulExecutions}
            icon={<CheckCircle fontSize="large" />}
            color="success.main"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Failed"
            value={stats.failedExecutions}
            icon={<Error fontSize="large" />}
            color="error.main"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Active Workflows"
            value={stats.activeWorkflows}
            icon={<PlayArrow fontSize="large" />}
            color="info.main"
          />
        </Grid>
      </Grid>

      {/* Recent Executions */}
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" component="h2" gutterBottom>
          Recent Executions
        </Typography>
        <Box>
          {recentExecutions.map((execution) => (
            <Box
              key={execution.id}
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                py: 2,
                borderBottom: '1px solid',
                borderColor: 'divider',
                '&:last-child': {
                  borderBottom: 'none',
                },
              }}
            >
              <Box>
                <Typography variant="subtitle1" component="div">
                  {execution.workflowName}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {execution.timestamp}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  {execution.duration}
                </Typography>
                {getStatusChip(execution.status)}
              </Box>
            </Box>
          ))}
        </Box>
      </Paper>
    </Box>
  );
};

export default Dashboard;
