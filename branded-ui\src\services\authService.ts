import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_N8N_API_URL || 'http://localhost:5678';

class AuthService {
  private token: string | null = null;

  constructor() {
    this.token = localStorage.getItem('qaflow_token');
    this.setupAxiosInterceptors();
  }

  private setupAxiosInterceptors() {
    // Request interceptor to add auth header
    axios.interceptors.request.use(
      (config) => {
        if (this.token) {
          config.headers.Authorization = `Basic ${this.token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle auth errors
    axios.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          this.logout();
        }
        return Promise.reject(error);
      }
    );
  }

  async login(username: string, password: string) {
    try {
      // Create basic auth token
      const token = btoa(`${username}:${password}`);
      
      // Test the credentials by making a request to n8n
      const response = await axios.get(`${API_BASE_URL}/rest/active-workflows`, {
        headers: {
          Authorization: `Basic ${token}`,
        },
      });

      if (response.status === 200) {
        this.token = token;
        localStorage.setItem('qaflow_token', token);
        
        return {
          success: true,
          user: {
            username,
            // Add more user info as needed
          },
        };
      } else {
        return {
          success: false,
          error: 'Invalid credentials',
        };
      }
    } catch (error: any) {
      console.error('Login error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Login failed',
      };
    }
  }

  async logout() {
    this.token = null;
    localStorage.removeItem('qaflow_token');
  }

  async validateToken(): Promise<boolean> {
    if (!this.token) {
      return false;
    }

    try {
      const response = await axios.get(`${API_BASE_URL}/rest/active-workflows`, {
        headers: {
          Authorization: `Basic ${this.token}`,
        },
      });
      return response.status === 200;
    } catch (error) {
      console.error('Token validation failed:', error);
      return false;
    }
  }

  async getCurrentUser() {
    // For basic auth, we don't have detailed user info
    // This is a placeholder for future enhancement
    return {
      username: 'admin', // Could be extracted from token if needed
    };
  }

  getToken(): string | null {
    return this.token;
  }
}

export const authService = new AuthService();
