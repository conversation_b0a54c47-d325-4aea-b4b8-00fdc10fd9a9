# QAFlow Caddy Configuration
# Handles reverse proxy, automatic HTTPS, and load balancing

{
    # Global options
    admin off
    auto_https off
}

# Development configuration (HTTP only)
:80 {
    # Handle branded UI
    handle /* {
        reverse_proxy branded-ui:3000 {
            header_up Host {host}
            header_up X-Real-IP {remote}
            header_up X-Forwarded-For {remote}
            header_up X-Forwarded-Proto {scheme}
        }
    }

    # Handle n8n API and webhooks
    handle /api/* {
        uri strip_prefix /api
        reverse_proxy n8n:5678 {
            header_up Host {host}
            header_up X-Real-IP {remote}
            header_up X-Forwarded-For {remote}
            header_up X-Forwarded-Proto {scheme}
        }
    }

    # Handle n8n webhooks
    handle /webhook/* {
        reverse_proxy n8n:5678 {
            header_up Host {host}
            header_up X-Real-IP {remote}
            header_up X-Forwarded-For {remote}
            header_up X-Forwarded-Proto {scheme}
        }
    }

    # Handle WebSocket connections
    handle /ws/* {
        reverse_proxy n8n:5678 {
            header_up Host {host}
            header_up X-Real-IP {remote}
            header_up X-Forwarded-For {remote}
            header_up X-Forwarded-Proto {scheme}
            header_up Connection {>Connection}
            header_up Upgrade {>Upgrade}
        }
    }

    # Health check endpoint
    handle /health {
        respond "QAFlow is healthy" 200
    }

    # Security headers
    header {
        # Remove server info
        -Server
        
        # Security headers
        X-Content-Type-Options nosniff
        X-Frame-Options DENY
        X-XSS-Protection "1; mode=block"
        Referrer-Policy "strict-origin-when-cross-origin"
        
        # CORS headers for development
        Access-Control-Allow-Origin "*"
        Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
        Access-Control-Allow-Headers "Content-Type, Authorization"
    }

    # Logging
    log {
        output file /var/log/caddy/access.log
        format json
    }
}

# Production configuration with automatic HTTPS
# Uncomment and modify for production use
# your-domain.com {
#     # Handle branded UI
#     handle /* {
#         reverse_proxy branded-ui:3000
#     }
#
#     # Handle n8n API
#     handle /api/* {
#         uri strip_prefix /api
#         reverse_proxy n8n:5678
#     }
#
#     # Handle webhooks
#     handle /webhook/* {
#         reverse_proxy n8n:5678
#     }
#
#     # Security headers for production
#     header {
#         Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
#         X-Content-Type-Options nosniff
#         X-Frame-Options DENY
#         X-XSS-Protection "1; mode=block"
#         Referrer-Policy "strict-origin-when-cross-origin"
#         Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline' fonts.googleapis.com; font-src 'self' fonts.gstatic.com; img-src 'self' data:; connect-src 'self'"
#     }
#
#     # Enable compression
#     encode gzip
#
#     # Logging
#     log {
#         output file /var/log/caddy/access.log
#         format json
#     }
# }
