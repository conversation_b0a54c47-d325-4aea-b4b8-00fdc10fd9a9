import React from 'react';
import {
  <PERSON>,
  Typography,
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON>,
  Grid,
  Chip,
} from '@mui/material';
import { Add, PlayArrow, Pause, Edit } from '@mui/icons-material';

const Workflows: React.FC = () => {
  // Mock data - replace with real data from n8n API
  const workflows = [
    {
      id: 1,
      name: 'PR Quality Check',
      description: 'Automated quality checks for pull requests',
      status: 'active',
      lastRun: '2024-01-15 14:30:00',
      executions: 45,
    },
    {
      id: 2,
      name: 'Nightly Test Suite',
      description: 'Comprehensive test suite running every night',
      status: 'active',
      lastRun: '2024-01-15 02:00:00',
      executions: 30,
    },
    {
      id: 3,
      name: 'API Integration Tests',
      description: 'Tests for API endpoints and integrations',
      status: 'inactive',
      lastRun: '2024-01-14 16:45:00',
      executions: 22,
    },
    {
      id: 4,
      name: 'Security Scan',
      description: 'Security vulnerability scanning workflow',
      status: 'active',
      lastRun: '2024-01-14 15:20:00',
      executions: 18,
    },
  ];

  const getStatusChip = (status: string) => {
    return status === 'active' ? (
      <Chip label="Active" color="success" size="small" />
    ) : (
      <Chip label="Inactive" color="default" size="small" />
    );
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Workflows
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage your QA automation workflows
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => {
            // TODO: Implement workflow creation
            alert('Workflow creation will be implemented in Phase 1');
          }}
        >
          Create Workflow
        </Button>
      </Box>

      <Grid container spacing={3}>
        {workflows.map((workflow) => (
          <Grid item xs={12} md={6} lg={4} key={workflow.id}>
            <Card>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                  <Typography variant="h6" component="h2">
                    {workflow.name}
                  </Typography>
                  {getStatusChip(workflow.status)}
                </Box>
                
                <Typography variant="body2" color="text.secondary" paragraph>
                  {workflow.description}
                </Typography>

                <Box mb={2}>
                  <Typography variant="body2" color="text.secondary">
                    Last run: {workflow.lastRun}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total executions: {workflow.executions}
                  </Typography>
                </Box>

                <Box display="flex" gap={1}>
                  <Button
                    size="small"
                    startIcon={workflow.status === 'active' ? <Pause /> : <PlayArrow />}
                    onClick={() => {
                      // TODO: Implement workflow start/stop
                      alert('Workflow control will be implemented in Phase 1');
                    }}
                  >
                    {workflow.status === 'active' ? 'Pause' : 'Start'}
                  </Button>
                  <Button
                    size="small"
                    startIcon={<Edit />}
                    onClick={() => {
                      // TODO: Implement workflow editing
                      alert('Workflow editing will be implemented in Phase 1');
                    }}
                  >
                    Edit
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default Workflows;
