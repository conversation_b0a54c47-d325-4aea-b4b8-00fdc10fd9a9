import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typography,
  Card,
  CardContent,
  Button,
  Grid,
  Chip,
  CircularProgress,
  Alert,
} from '@mui/material';
import { Add, PlayArrow, Pause, Edit } from '@mui/icons-material';
import { apiService } from '../../services/apiService';

const Workflows: React.FC = () => {
  const [flows, setFlows] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    loadFlows();
  }, []);

  const loadFlows = async () => {
    try {
      setLoading(true);
      const data = await apiService.getFlows();
      setFlows(data);
      setError('');
    } catch (err) {
      console.error('Failed to load flows:', err);
      setError('Failed to load flows. Using mock data.');
      // Fallback to mock data
      setFlows(mockFlows);
    } finally {
      setLoading(false);
    }
  };

  // Mock data - fallback when API is not available
  const mockFlows = [
    {
      id: 1,
      name: 'PR Quality Check',
      description: 'Automated quality checks for pull requests',
      status: 'active',
      lastRun: '2024-01-15 14:30:00',
      executions: 45,
    },
    {
      id: 2,
      name: 'Nightly Test Suite',
      description: 'Comprehensive test suite running every night',
      status: 'active',
      lastRun: '2024-01-15 02:00:00',
      executions: 30,
    },
    {
      id: 3,
      name: 'API Integration Tests',
      description: 'Tests for API endpoints and integrations',
      status: 'inactive',
      lastRun: '2024-01-14 16:45:00',
      executions: 22,
    },
    {
      id: 4,
      name: 'Security Scan',
      description: 'Security vulnerability scanning workflow',
      status: 'active',
      lastRun: '2024-01-14 15:20:00',
      executions: 18,
    },
  ];

  const getStatusChip = (status: string) => {
    return status === 'active' ? (
      <Chip label="Active" color="success" size="small" />
    ) : (
      <Chip label="Inactive" color="default" size="small" />
    );
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Flows
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage your QA automation flows
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => {
            // TODO: Implement flow creation
            alert('Flow creation will be implemented in Phase 1');
          }}
        >
          Create Flow
        </Button>
      </Box>

      {error && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {loading ? (
        <Box display="flex" justifyContent="center" py={4}>
          <CircularProgress />
        </Box>
      ) : (
        <Grid container spacing={3}>
          {flows.map((flow: any) => (
            <Grid item xs={12} md={6} lg={4} key={flow.id || flow.name}>
              <Card>
                <CardContent>
                  <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                    <Typography variant="h6" component="h2">
                      {flow.displayName || flow.name}
                    </Typography>
                    {getStatusChip(flow.status || 'active')}
                  </Box>

                  <Typography variant="body2" color="text.secondary" paragraph>
                    {flow.description || 'QA automation flow'}
                  </Typography>

                  <Box mb={2}>
                    <Typography variant="body2" color="text.secondary">
                      Last run: {flow.lastRun || 'Never'}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total executions: {flow.executions || 0}
                    </Typography>
                  </Box>

                  <Box display="flex" gap={1}>
                    <Button
                      size="small"
                      startIcon={flow.status === 'active' ? <Pause /> : <PlayArrow />}
                      onClick={() => {
                        // TODO: Implement flow start/stop
                        alert('Flow control will be implemented in Phase 1');
                      }}
                    >
                      {flow.status === 'active' ? 'Pause' : 'Start'}
                    </Button>
                    <Button
                      size="small"
                      startIcon={<Edit />}
                      onClick={() => {
                        // TODO: Implement flow editing
                        alert('Flow editing will be implemented in Phase 1');
                      }}
                    >
                      Edit
                    </Button>
                  </Box>
              </CardContent>
            </Card>
          </Grid>
          ))}
        </Grid>
      )}
    </Box>
  );
};

export default Workflows;
