# QAFlow MVP Environment Configuration
# Copy this file to .env and update the values

# =============================================================================
# BASIC CONFIGURATION
# =============================================================================

# Domain and Protocol
N8N_HOST=localhost
N8N_PROTOCOL=http
WEBHOOK_URL=http://localhost:5678

# Timezone
TIMEZONE=UTC

# Node Environment
NODE_ENV=production

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================

# n8n Basic Auth (Change these!)
N8N_USER=admin
N8N_PASSWORD=change_this_strong_password

# Encryption key for n8n (Generate a random 32-character string)
N8N_ENCRYPTION_KEY=your_32_character_encryption_key_here

# Enable secure cookies (set to true for HTTPS)
N8N_SECURE_COOKIE=false

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL
POSTGRES_PASSWORD=change_this_postgres_password
POSTGRES_PORT=5432

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================

# Redis (for queue management)
REDIS_PASSWORD=change_this_redis_password
REDIS_PORT=6379

# =============================================================================
# SERVICE PORTS
# =============================================================================

# n8n port
N8N_PORT=5678

# Branded UI port
UI_PORT=3000

# API base URL
API_BASE_URL=http://localhost:3000

# =============================================================================
# AI PROVIDER CONFIGURATION
# =============================================================================

# Default AI Provider (groq, openai, anthropic)
DEFAULT_AI_PROVIDER=groq

# Groq Configuration
GROQ_API_KEY=your_groq_api_key_here
GROQ_MODEL=llama3-8b-8192

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4

# Anthropic Configuration
ANTHROPIC_API_KEY=your_anthropic_api_key_here
ANTHROPIC_MODEL=claude-3-sonnet-20240229

# =============================================================================
# NOTIFICATION CONFIGURATION
# =============================================================================

# Email/SMTP
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM=QAFlow <<EMAIL>>

# Slack
SLACK_BOT_TOKEN=xoxb-your-slack-bot-token
SLACK_CHANNEL=#qa-alerts

# Microsoft Teams
TEAMS_WEBHOOK_URL=https://your-org.webhook.office.com/webhookb2/...

# =============================================================================
# CI/CD INTEGRATION
# =============================================================================

# GitHub
GITHUB_TOKEN=your_github_token
GITHUB_WEBHOOK_SECRET=your_webhook_secret

# GitLab
GITLAB_TOKEN=your_gitlab_token
**********************your_webhook_secret

# Bitbucket
BITBUCKET_USERNAME=your_username
BITBUCKET_APP_PASSWORD=your_app_password

# Azure DevOps
AZURE_DEVOPS_TOKEN=your_azure_devops_token
AZURE_DEVOPS_ORG=your_organization

# Jenkins
JENKINS_URL=https://your-jenkins.com
JENKINS_USER=your_jenkins_user
JENKINS_TOKEN=your_jenkins_token

# =============================================================================
# ISSUE TRACKER INTEGRATION
# =============================================================================

# Jira
JIRA_HOST=https://your-company.atlassian.net
JIRA_EMAIL=<EMAIL>
JIRA_API_TOKEN=your_jira_api_token

# Trello
TRELLO_API_KEY=your_trello_api_key
TRELLO_TOKEN=your_trello_token

# =============================================================================
# TEST EXECUTION
# =============================================================================

# Test execution timeout (in seconds)
TEST_TIMEOUT=3600

# Test results storage path
TEST_RESULTS_PATH=/tmp/qaflow/results

# Playwright/Selenium configuration
BROWSER_HEADLESS=true
BROWSER_TIMEOUT=30000

# =============================================================================
# MONITORING & LOGGING
# =============================================================================

# Log level (error, warn, info, debug)
LOG_LEVEL=info

# Enable metrics collection
ENABLE_METRICS=true

# Retention period for execution logs (days)
LOG_RETENTION_DAYS=30

# =============================================================================
# ADVANCED CONFIGURATION
# =============================================================================

# Multi-tenancy
ENABLE_MULTI_TENANT=false
DEFAULT_TENANT_ID=default

# Rate limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=900000

# File upload limits
MAX_FILE_SIZE=10MB
ALLOWED_FILE_TYPES=.json,.xml,.txt,.log,.zip

# =============================================================================
# DEVELOPMENT ONLY
# =============================================================================

# Enable debug mode (development only)
DEBUG_MODE=false

# Hot reload for UI (development only)
REACT_APP_HOT_RELOAD=false
